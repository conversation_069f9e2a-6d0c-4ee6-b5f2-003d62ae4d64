package main

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis"
)

const (
	MAX_BET_CONFIG_COUNT = 15 // 下注选项数目
)

const (
	ICON_FLOWER_RED     = 1
	ICON_FLOWER_SILVERY = 2
	ICON_FLOWER_YELLOW  = 3
	ICON_WILD           = 4
	ICON_WILD_X3        = 5
	ICON_WILD_X5        = 6
	ICON_WILD_X9        = 7
)

// 单位元
var betConfigCount = [MAX_BET_CONFIG_COUNT]int{1, 2, 3, 5, 8, 10, 20, 50, 100, 200, 300, 500, 800, 1000, 2000}

var odd_config = [5]int{100, 500, 2100, 11100, 111100}

type UserGameInfo struct {
	Bet       int // 下注数目
	Result    int // 总奖励
	PayOut    int // 实际输赢
	PlateWin  int
	Icons     [4]int
	AwardType int
}

func (gameInfo *UserGameInfo) Reset() {

	gameInfo.Result = 0
	gameInfo.PayOut = 0
	gameInfo.AwardType = -1
	gameInfo.PlateWin = 0
}
func init() {
	rand.Seed(time.Now().UnixNano())
}

func RandUInt() uint32 {
	const mask = 0x00000FFF
	var result uint32
	result |= uint32(rand.Intn(mask + 1))
	result |= uint32(rand.Intn(mask+1)) << 12
	result |= uint32(rand.Intn(mask+1)) << 24
	return result
}

// abs 返回整数的绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

// getRand 返回一个在[nMin, nMax]范围内的随机整数
func getRand(nMin, nMax int) int {
	if nMin > nMax {
		nMin, nMax = nMax, nMin
	}
	nDiff := abs(nMax-nMin) + 1
	result := (int)(RandUInt()%uint32(nDiff) + uint32(nMin))
	return result
}

func get_normal_rand_icon() int {
	vec := []int{1800, 1500, 1200, 1000, 800, 500, 400, 100}
	sum := 0

	for _, num := range vec {
		sum += num
	}
	r := getRand(0, sum)
	for i := 0; i < len(vec); i++ {
		if r < vec[i] {
			return i
		} else {
			r -= vec[i]
		}
	}
	return 0
}

func RandFillIcons(icon *[3]int) {

	for i := 0; i < 3; i++ {
		(*icon)[i] = get_normal_rand_icon()
	}

}

func CalcResultTimes(gameInfo *UserGameInfo) int {
	gameInfo.Result = 0
	gameInfo.AwardType = -1
	betmul := gameInfo.Bet / 100
	iconCount := make([]int, 9+1) // 统计图标个数
	allIcon := 0                  // 中间行图标个数

	for i := 0; i < 3; i++ {
		if gameInfo.Icons[i] == 0 {
			continue
		}
		iconCount[gameInfo.Icons[i]]++
		allIcon++
	}

	if allIcon < 3 { // 少于三个，没中奖
		return 0
	}

	// 判断是否是3个wild
	nWildCount := iconCount[ICON_WILD] + iconCount[ICON_WILD_X3] + iconCount[ICON_WILD_X5] + iconCount[ICON_WILD_X9]
	if nWildCount == 3 {
		gameInfo.PlateWin = odd_config[ICON_WILD]
		gameInfo.Result = gameInfo.PlateWin
		gameInfo.AwardType = ICON_WILD
		gameInfo.Icons[0] = ICON_WILD
		gameInfo.Icons[1] = ICON_WILD
		gameInfo.Icons[2] = ICON_WILD
		return gameInfo.Result
	}
	nWildMul := 0
	nWildMul += iconCount[ICON_WILD_X3] * 3
	nWildMul += iconCount[ICON_WILD_X5] * 5
	nWildMul += iconCount[ICON_WILD_X9] * 9
	if nWildMul == 0 {
		nWildMul = 1
	}
	// 先判断是否是三个相等
	threeSame := false
	for i := 1; i <= ICON_FLOWER_YELLOW; i++ {
		if (iconCount[i] + nWildCount) == 3 {
			gameInfo.PlateWin = odd_config[i] * nWildMul
			threeSame = true
			gameInfo.AwardType = i
		}
	}

	if !threeSame { // 三个不相同的
		gameInfo.PlateWin = odd_config[0] * nWildMul
		gameInfo.AwardType = 0
	}

	gameInfo.PlateWin = gameInfo.PlateWin * betmul
	gameInfo.Result = gameInfo.PlateWin
	return gameInfo.Result
}

func write_normal_data_record_to_redis(mMapResult map[int][]string) {
	//写redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     "47.236.236.24:6379",             // Redis 服务器地址
		Password: "E8LmKBGgoVm00KYeVDao03NRkPXmF8", // Redis 服务器密码（如果没有密码，可以留空）
		DB:       3,                                // 使用默认数据库
	})
	defer rdb.Close()
	//普通玩法记录写redis
	var hashKey string
	hashKey = "CBT_"
	for outerKey, stringvalue := range mMapResult {
		strKey := hashKey + strconv.Itoa(outerKey)

		tempMap := make(map[int]string)

		for i, str := range stringvalue {
			parts := strings.Split(str, ",")
			intSlice := make([]int, len(parts))
			// 将每个部分转换为整数
			for j, part := range parts {
				num, err := strconv.Atoi(part)
				if err != nil {
					fmt.Printf("Error converting string '%s' to int: %v\n", part, err)
					return
				}
				intSlice[j] = num
			}
			// 将转换后的整数切片存储到二维切片中
			intMatrix := make([][]int, 1)
			intMatrix[0] = intSlice
			jsonData, _ := json.Marshal(intMatrix)
			tempMap[i] = string(jsonData)

		}
		elements := make([]interface{}, 0, len(tempMap))
		nCount := 0
		push_len := 30
		// 遍历 map，将数据转换为字符串格式并添加到切片中
		for _, value := range tempMap {

			elements = append(elements, value)
			nCount++
			if (nCount % push_len) == 0 {
				err := rdb.RPush(strKey, elements...).Err()
				if err != nil {
					//panic(err)
				}
				elements = elements[:0]
			}
		}
		err := rdb.RPush(strKey, elements...).Err()
		if err != nil {
			//panic(err)
		}
	}
}

int game_logic::CalcResultTimes(user_game_info &game_info, int icons[3][3])
{   
	game_info.result = 0;
	game_info.award_type = -1;
	int mul = game_info.bet / 100;
	int iconCount[ICON_WILD + 1] = { 0 }; //统计图标个数
	int all_icon = 0;  //中间行图标个数
	int bonus_count = 0; //bonus图标个数
	for (int i = 0; i < 3; i++)
	{
		for (int j = 0; j < 3; j++)
		{	
			if (icons[i][j] == ICON_BONUS_1)
			{
				bonus_count++;
			}
			
		}

	}

	iconCount[icons[0][1]]++;
	iconCount[icons[1][1]]++;
	iconCount[icons[2][1]]++;

	int multi_num[] = {2,3,5};
	int nMul = 0;
	int nWildCount = 0;
	nWildCount += iconCount[ICON_WILD];
	nWildCount += iconCount[ICON_WILD_2X];
	nWildCount += iconCount[ICON_WILD_3X];
	nWildCount += iconCount[ICON_WILD_5X];

	for (int i=0; i<nWildCount; i++)
	{
		int index = getRand(0, 2);
		nMul += multi_num[index];
	}
	if (nMul == 0)
	{
		nMul = 1;
	}
	game_info.mul = nMul;

	if (bonus_count >= 3)
	{
		
	}
	if (icons[0][1] == ICON_BLANK || icons[1][1] == ICON_BLANK || icons[2][1] == ICON_BLANK)
	{
		return 0;
	}
	//判断是否是3个wild
	if (nWildCount == 3)
	{
		game_info.plate_win = 5;
		game_info.result = game_info.plate_win;
		game_info.award_type = ICON_WILD;
		return game_info.result;
	}

	int award_icon = 0;//中奖的icon
	//先判断是否是三个相等
	bool threeSame = false;
	for (int i = 0; i < 3; i++)
	{
		int id = icons[i][1];
		if ((iconCount[id] + nWildCount) == 3)
		{
			game_info.plate_win = get_odd(id) * nMul;
			threeSame = true;
			game_info.award_type = i;
		}
	}
	if (!threeSame) //三个不相同的
	{
		game_info.plate_win = odd_info[0] * nMul;
		game_info.award_type = 0;
	

	}
	game_info.plate_win = game_info.plate_win * mul;
	game_info.round_result = game_info.plate_win;
	game_info.result += game_info.plate_win;
	return game_info.result;
	
}


func main() {
	mMapResult := make(map[int][]string)
	mMapStr := make(map[string]int)
	//mMapStr := make(map[int]map[string]int)

	szICon := [3]int{0, 0, 0}

	for i := 1; i <= 7; i++ {
		if i >= ICON_WILD_X3 && i <= ICON_WILD_X9 {
			continue
		}
		szICon[0] = i
		for j := 1; j <= 7; j++ {
			szICon[1] = j
			for z := 1; z <= 7; z++ {
				if z >= ICON_WILD_X3 && z <= ICON_WILD_X9 {
					continue
				}
				szICon[2] = z
				var gameInfo UserGameInfo
				gameInfo.Bet = 100
				for y := 0; y < 3; y++ {
					gameInfo.Icons[y] = szICon[y]
				}
				CalcResultTimes(&gameInfo)
				var strResultBuilder strings.Builder
				strResultBuilder.Reset()
				for y := 0; y < 3; y++ {
					strResultBuilder.WriteString(fmt.Sprintf("%d,", gameInfo.Icons[y]))
				}
				strResult := strResultBuilder.String()
				if len(strResult) == 0 {
					continue
				}
				strResult = strResult[:len(strResult)-1]
				if _, exists := mMapStr[strResult]; exists {
					continue
				}
				mMapStr[strResult] = 1
				if gameInfo.Result > 0 {
					if gameInfo.Result <= 2000 {
						if len(mMapResult[gameInfo.Result]) <= 200 {
							mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
						}
					} else if gameInfo.Result > 2000 && gameInfo.Result <= 5000 {
						if len(mMapResult[gameInfo.Result]) <= 100 {
							mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
						}
					} else {
						if len(mMapResult[gameInfo.Result]) <= 50 {
							mMapResult[gameInfo.Result] = append(mMapResult[gameInfo.Result], strResult)
						}
					}

				}
			}
		}
	}

	write_normal_data_record_to_redis(mMapResult)

}
